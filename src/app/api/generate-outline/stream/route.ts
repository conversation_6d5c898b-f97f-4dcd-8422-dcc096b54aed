import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

/**
 * 流式生成智能大纲
 */
async function streamOutlineGeneration(content: string, title: string) {
  // 检查是否有有效的OpenAI API密钥
  const apiKey = process.env.OPENAI_API_KEY
  if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
    // 返回模拟的流式响应
    return createMockStream(content)
  }

  try {
    const systemPrompt = `你是一个专业的内容分析师，擅长为文章生成清晰的大纲结构。

请分析用户提供的内容，逐步生成一个层次分明的大纲。要求：

1. 逐步识别文章的主要章节和子章节
2. 为每个章节生成简洁明了的标题
3. 确保大纲层次清晰（1-4级标题）
4. 标题应该准确概括对应内容的核心要点
5. 以流式方式输出，每次输出一个完整的大纲项

输出格式：每行一个JSON对象，格式如下：
{"id": "section-id", "title": "章节标题", "level": 1}
{"id": "subsection-id", "title": "子章节标题", "level": 2}

注意：
- id应该是英文，使用连字符分隔
- title应该是中文，简洁明了
- level范围是1-4，表示标题层级
- 每行输出一个完整的JSON对象`

    return await openai.chat.completions.create({
      model: process.env.OUTLINE_GENERATION_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `请为以下内容逐步生成大纲：

**标题：** ${title}

**内容：**
${content}

请以流式方式输出大纲，每行一个JSON对象。`
        }
      ],
      max_tokens: parseInt(process.env.OUTLINE_MAX_TOKENS || '1000'),
      temperature: parseFloat(process.env.OUTLINE_TEMPERATURE || '0.3'),
      stream: true
    })

  } catch (error) {
    console.error('流式生成大纲失败:', error)
    return createMockStream(content)
  }
}

/**
 * 创建模拟的流式响应
 */
async function* createMockStream(content: string) {
  // 基于内容生成简单大纲
  const lines = content.split('\n')
  const headings: Array<{id: string, title: string, level: number}> = []

  lines.forEach((line, index) => {
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/)
    if (headingMatch) {
      const level = headingMatch[1].length
      const title = headingMatch[2].trim()
      // 统一的ID生成函数
      const generateHeadingId = (title: string) => {
        const cleanTitle = title
          .toLowerCase()
          .trim()
          .replace(/[\s（）()：:，,。.！!？?；;]+/g, '-') // 替换各种标点和空格为连字符
          .replace(/[^\w\u4e00-\u9fff-]/g, '') // 保留字母、数字、中文字符和连字符
          .replace(/-+/g, '-') // 合并多个连字符
          .replace(/^-|-$/g, '') // 移除开头和结尾的连字符

        if (!cleanTitle || cleanTitle.length < 2) {
          return title.replace(/[^\w\u4e00-\u9fff]/g, '').substring(0, 20) || 'heading'
        }

        return cleanTitle.substring(0, 50)
      }

      const id = generateHeadingId(title)
      headings.push({ id: id || `heading-${index}`, title, level })
    }
  })

  // 如果没有找到标题，生成默认大纲
  if (headings.length === 0) {
    headings.push(
      { id: 'introduction', title: '内容概述', level: 1 },
      { id: 'main-content', title: '主要内容', level: 1 },
      { id: 'conclusion', title: '总结', level: 1 }
    )
  }

  // 模拟流式输出
  for (const heading of headings) {
    yield {
      choices: [{
        delta: {
          content: JSON.stringify(heading) + '\n'
        }
      }]
    }
    // 添加延迟以模拟真实的流式响应
    await new Promise(resolve => setTimeout(resolve, 100))
  }
}

/**
 * 解析流式内容中的大纲项
 */
function parseOutlineFromStream(content: string): Array<{id: string, title: string, level: number}> {
  const lines = content.split('\n').filter(line => line.trim())
  const outline: Array<{id: string, title: string, level: number}> = []

  for (const line of lines) {
    try {
      const item = JSON.parse(line.trim())
      if (item.id && item.title && item.level) {
        outline.push({
          id: item.id,
          title: item.title,
          level: Math.max(1, Math.min(4, item.level))
        })
      }
    } catch (error) {
      // 忽略解析错误的行
      continue
    }
  }

  return outline
}

export async function POST(request: NextRequest) {
  try {
    const { content, title } = await request.json()
    
    if (!content) {
      return NextResponse.json({ error: '缺少内容参数' }, { status: 400 })
    }

    // 获取流式响应
    const stream = await streamOutlineGeneration(content, title || '未命名文档')
    
    // 创建响应流
    const encoder = new TextEncoder()
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          let accumulatedContent = ''
          
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || ''
            accumulatedContent += content
            
            // 尝试解析当前累积的内容
            const currentOutline = parseOutlineFromStream(accumulatedContent)
            
            const streamData = {
              type: 'outline_update',
              outline: currentOutline,
              rawContent: accumulatedContent
            }
            
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(streamData)}\n\n`))
          }
          
          // 发送完成信号
          const finalOutline = parseOutlineFromStream(accumulatedContent)
          const finalData = {
            type: 'outline_complete',
            outline: finalOutline,
            success: true
          }
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalData)}\n\n`))
          
        } catch (error) {
          console.error('流式生成大纲失败:', error)
          const errorData = {
            type: 'error',
            error: error instanceof Error ? error.message : '生成大纲失败'
          }
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`))
        } finally {
          controller.close()
        }
      }
    })

    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('生成大纲失败:', error)
    return NextResponse.json({ 
      error: '生成大纲失败，请稍后重试',
      outline: []
    }, { status: 500 })
  }
}
