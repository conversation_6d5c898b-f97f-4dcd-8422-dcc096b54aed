'use client'

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { List } from 'lucide-react'

interface OutlineItem {
  id: string
  title: string
  level: number
}

interface OutlineCardProps {
  outline: OutlineItem[]
  onHeadingClick: (headingId: string) => void
  isLoading?: boolean
}

const OutlineCard: React.FC<OutlineCardProps> = ({ outline, onHeadingClick, isLoading = false }) => {
  const [activeHeading, setActiveHeading] = useState<string | null>(null)
  const [visibleCount, setVisibleCount] = useState(0)
  const [cardHeight, setCardHeight] = useState<string>('auto')
  const contentRef = useRef<HTMLDivElement>(null)
  const streamingTimerRef = useRef<NodeJS.Timeout | null>(null)

  // 优化的流式显示逻辑 - 避免状态更新循环
  useEffect(() => {
    // 清除之前的定时器
    if (streamingTimerRef.current) {
      clearTimeout(streamingTimerRef.current)
      streamingTimerRef.current = null
    }

    if (outline.length === 0) {
      setVisibleCount(0)
      return
    }

    // 如果是新的大纲内容，重置可见数量并开始流式显示
    if (visibleCount === 0 || visibleCount > outline.length) {
      setVisibleCount(1) // 立即显示第一个条目

      // 开始流式显示剩余条目
      if (outline.length > 1) {
        const showNextItem = (currentCount: number) => {
          if (currentCount < outline.length) {
            streamingTimerRef.current = setTimeout(() => {
              setVisibleCount(currentCount + 1)
              showNextItem(currentCount + 1)
            }, 150)
          }
        }
        showNextItem(1)
      }
    }

    return () => {
      if (streamingTimerRef.current) {
        clearTimeout(streamingTimerRef.current)
        streamingTimerRef.current = null
      }
    }
  }, [outline.length]) // 只依赖outline.length，避免visibleCount导致的循环

  // 优化的动态高度计算 - 使用useCallback避免重复创建函数
  const calculateOptimalHeight = useCallback(() => {
    if (!contentRef.current) return

    // 获取主内容区域的高度
    const mainContent = document.querySelector('.flex-1.overflow-y-auto') as HTMLElement
    if (!mainContent) return

    const mainContentHeight = mainContent.scrollHeight
    const viewportHeight = window.innerHeight

    // 计算基础参数
    const topOffset = 24 // top-6 = 1.5rem = 24px
    const bottomPadding = 48 // 底部留白
    const parentPadding = 48 // 父容器的 p-6 = 1.5rem * 2 = 48px

    // 设置最小和最大高度
    const minHeight = 300
    const maxHeight = viewportHeight - topOffset - bottomPadding - parentPadding

    // 根据内容长度动态调整
    let optimalHeight: number

    if (mainContentHeight <= viewportHeight * 0.6) {
      // 短内容：使用较小的高度
      optimalHeight = Math.min(mainContentHeight * 0.8, minHeight * 1.5)
    } else if (mainContentHeight <= viewportHeight * 1.2) {
      // 中等内容：使用中等高度
      optimalHeight = Math.min(mainContentHeight * 0.6, maxHeight * 0.7)
    } else {
      // 长内容：使用最大高度
      optimalHeight = maxHeight
    }

    // 确保在最小和最大高度范围内
    optimalHeight = Math.max(minHeight, Math.min(optimalHeight, maxHeight))

    setCardHeight(`${optimalHeight}px`)
  }, [])

  // 动态计算大纲卡片高度 - 减少依赖，避免频繁重新计算
  useEffect(() => {
    // 初始计算
    calculateOptimalHeight()

    // 监听窗口大小变化
    window.addEventListener('resize', calculateOptimalHeight)

    // 延迟重新计算，确保内容已渲染
    const timer = setTimeout(calculateOptimalHeight, 100)

    return () => {
      window.removeEventListener('resize', calculateOptimalHeight)
      clearTimeout(timer)
    }
  }, [calculateOptimalHeight, outline.length]) // 只在大纲长度变化时重新计算

  const handleHeadingClick = useCallback((headingId: string) => {
    setActiveHeading(headingId)
    onHeadingClick(headingId)
  }, [onHeadingClick])

  const getIndentClass = (level: number) => {
    switch (level) {
      case 1: return 'pl-0'
      case 2: return 'pl-4'
      case 3: return 'pl-8'
      case 4: return 'pl-12'
      case 5: return 'pl-16'
      case 6: return 'pl-20'
      default: return 'pl-0'
    }
  }

  const getFontSizeClass = (level: number) => {
    switch (level) {
      case 1: return 'text-base font-semibold'
      case 2: return 'text-sm font-medium'
      case 3: return 'text-sm'
      case 4: return 'text-xs'
      case 5: return 'text-xs'
      case 6: return 'text-xs'
      default: return 'text-sm'
    }
  }

  if (outline.length === 0 && !isLoading) return null

  return (
    <div className="sticky top-6" style={{ height: cardHeight }}>
      {/* 毛玻璃效果的大圆角卡片 */}
      <div className="backdrop-blur-xl bg-white/90 border border-gray-200/30 rounded-3xl shadow-2xl overflow-hidden ring-1 ring-white/20 h-full flex flex-col">


        {/* 大纲内容 */}
        <div ref={contentRef} className="p-4 overflow-y-auto flex-1">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-sm text-gray-600">正在生成智能大纲...</span>
                </div>
              </div>
            ) : (
              <div className="space-y-1">
                {outline.slice(0, visibleCount).map((item, index) => (
                  <div
                    key={`${item.id}-${index}`}
                    className={cn(
                      "opacity-0 transform translate-x-2",
                      index < visibleCount ? "animate-fade-in-smooth" : ""
                    )}
                    style={{
                      animationDelay: `${index * 100}ms`,
                      animationFillMode: 'forwards'
                    }}
                  >
                    <button
                      onClick={() => handleHeadingClick(item.id)}
                      className={cn(
                        "w-full text-left py-2 px-3 rounded-lg transition-all duration-200 hover:bg-blue-50/50 group",
                        getIndentClass(item.level),
                        activeHeading === item.id
                          ? "bg-blue-100/50 text-blue-700 border-l-2 border-blue-500"
                          : "text-gray-700 hover:text-gray-900"
                      )}
                    >
                      <div className={cn(
                        "truncate",
                        getFontSizeClass(item.level)
                      )}>
                        {item.title}
                      </div>
                      {item.level === 1 && (
                        <div className="w-full h-px bg-gray-200/50 mt-1"></div>
                      )}
                    </button>
                  </div>
                ))}
              </div>
            )}
        </div>

        {/* 卡片底部装饰 */}
        <div className="h-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500"></div>
      </div>
    </div>
  )
}

export default OutlineCard
